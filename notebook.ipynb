"""
DATA LOADING AND INITIAL EXPLORATION
====================================

This section loads the crime dataset and performs initial data exploration.
We import essential libraries for data manipulation, analysis, and visualization.

Key Libraries:
- pandas: Data manipulation and analysis
- numpy: Numerical computing
- seaborn: Statistical data visualization (primary visualization library)

Data Loading Notes:
- TIME OCC is loaded as string to preserve leading zeros (e.g., '0100' for 1:00 AM)
- This prevents automatic conversion to integer which would lose time formatting
"""

# Import required libraries for data analysis and visualization
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

# Configure seaborn visualization settings for professional presentation
sns.set_theme(style="whitegrid", palette="husl")
sns.set_context("notebook", font_scale=1.1, rc={"figure.figsize": (12, 8)})

# Load the crime dataset
# Note: TIME OCC is loaded as string to preserve time formatting (e.g., '0100' for 1:00 AM)
crimes = pd.read_csv("crimes.csv", dtype={"TIME OCC": str})

# Display basic information about the dataset
print(f"Dataset Shape: {crimes.shape[0]:,} rows × {crimes.shape[1]} columns")
print(f"Memory Usage: {crimes.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
print("\n📊 First 5 rows of the dataset:")
crimes.head()

"""
PEAK CRIME HOUR ANALYSIS
========================

This analysis extracts the hour from the TIME OCC column and identifies
the hour with the highest frequency of crimes using clean, readable code.

Steps:
1. Parse TIME OCC string to extract hour (first 2 characters)
2. Convert to integer for numerical analysis
3. Calculate hourly crime frequency
4. Identify peak hour and generate insights
"""

# Extract hour from TIME OCC column (24-hour format)
# TIME OCC format: 'HHMM' (e.g., '1200' = 12:00 PM, '0100' = 1:00 AM)
crimes["HOUR_OCC"] = crimes["TIME OCC"].str[:2].astype(int)

# Calculate hourly crime frequency and sort by hour
hourly_counts = crimes["HOUR_OCC"].value_counts().sort_index()

# Identify peak crime hour (hour with maximum crimes)
peak_crime_hour = hourly_counts.idxmax()
peak_hour_crimes = hourly_counts[peak_crime_hour]

# Display comprehensive results
print("🚨 PEAK CRIME HOUR ANALYSIS RESULTS")
print("=" * 40)
print(f"Peak Crime Hour: {peak_crime_hour}:00 (24-hour format)")
print(f"Total Crimes at Peak Hour: {peak_hour_crimes:,}")
print(f"Percentage of Total Crimes: {(peak_hour_crimes/len(crimes)*100):.1f}%")
print(f"\n📊 This represents {peak_hour_crimes:,} out of {len(crimes):,} total crimes")

"""
TEMPORAL CRIME PATTERN VISUALIZATION
====================================

This section creates a comprehensive visualization showing crime frequency
distribution across all 24 hours using seaborn for professional presentation.

Visualization Features:
- Bar plot showing crime distribution by hour using seaborn
- Clean, readable code with seaborn styling
- Statistical summary of temporal crime patterns
"""

# Create a DataFrame for better seaborn integration
hourly_data = pd.DataFrame({
    'Hour': hourly_counts.index,
    'Crime_Count': hourly_counts.values
})

# Add a column to highlight the peak hour
hourly_data['Is_Peak'] = hourly_data['Hour'] == peak_crime_hour

fig, ax = plt.subplots(figsize=(12, 8))
# Create bar plot with seaborn
sns.barplot(
    data=hourly_data, 
    x='Hour', 
    y='Crime_Count',
    hue='Is_Peak',
    palette={False: 'steelblue', True: 'crimson'},
    legend=False,
    ax=ax
)

# Customize the plot
ax.set_title(
    "📊 Crime Frequency Distribution by Hour of Day\nLos Angeles Crime Data Analysis", 
    fontsize=16, fontweight='bold', pad=20
)
ax.set_xlabel("Hour of Day (24-hour format)", fontsize=12, fontweight='bold')
ax.set_ylabel("Number of Crimes", fontsize=12, fontweight='bold')

# Add value labels on bars for key hours
for i, (hour, count) in enumerate(zip(hourly_data['Hour'], hourly_data['Crime_Count'])):
    if hour == peak_crime_hour:  # Only label the peak hour for clarity
        ax.text(i, count + 50, f'{count:,}\n(Peak)', 
               ha='center', va='bottom', fontweight='bold', fontsize=10)

# Format x-axis labels
ax.set_xticks(range(0, 24, 2))  # Show every 2 hours for clarity
ax.set_xticklabels([f'{h:02d}:00' for h in range(0, 24, 2)], rotation=45)

plt.tight_layout()
plt.show()

# Display comprehensive statistical summary
print("\n📈 HOURLY CRIME STATISTICS SUMMARY")
print("=" * 50)
print(f"Total Crimes Analyzed: {len(crimes):,}")
print(f"Peak Crime Hour: {peak_crime_hour}:00 ({peak_hour_crimes:,} crimes)")
print(f"Lowest Crime Hour: {hourly_counts.idxmin()}:00 ({hourly_counts.min():,} crimes)")
print(f"Average Crimes per Hour: {hourly_counts.mean():.0f}")
print(f"Standard Deviation: {hourly_counts.std():.0f}")

# Display top crime hours
print("\n🏆 TOP 5 CRIME HOURS:")
print("-" * 30)
top_hours = hourly_counts.sort_values(ascending=False).head(5)
for i, (hour, count) in enumerate(top_hours.items(), 1):
    percentage = (count / len(crimes)) * 100
    print(f"{i}. Hour {hour:02d}:00 - {count:,} crimes ({percentage:.1f}%)")

# Generate business insights based on peak hour
print("\n💡 BUSINESS INSIGHTS:")
print("-" * 20)
if 12 <= peak_crime_hour <= 18:
    print("• Peak crime occurs during afternoon/evening hours")
    print("• Recommend increased patrol presence during business hours")
elif peak_crime_hour >= 19 or peak_crime_hour <= 5:
    print("• Peak crime occurs during night/early morning hours")
    print("• Recommend enhanced night patrol and lighting in high-crime areas")
else:
    print("• Peak crime occurs during morning hours")
    print("• Consider morning patrol adjustments and community awareness programs")

"""
NIGHT CRIME HOTSPOT ANALYSIS
============================

This analysis identifies areas with the highest frequency of night crimes,
defined as crimes occurring between 10 PM (22:00) and 3:59 AM.
Uses clean, readable code with efficient pandas operations.

Night Crime Definition:
- Time window: 22:00 - 03:59 (10 PM - 3:59 AM)
- Includes late evening and early morning hours
- Critical for night patrol resource allocation

Analysis Steps:
1. Define night hours using clear criteria
2. Filter dataset for night crimes
3. Calculate area-wise night crime frequency
4. Identify peak night crime location
"""

# Define night crime hours (22:00-03:59)
# Using isin() for cleaner, more readable code
night_hours = list(range(22, 24)) + list(range(0, 4))  # [22, 23, 0, 1, 2, 3]
crimes["NIGHT_CRIME"] = crimes["HOUR_OCC"].isin(night_hours)

# Filter for night crimes only
night_crimes = crimes[crimes["NIGHT_CRIME"]]

# Calculate night crime frequency by area
night_crime_by_area = night_crimes["AREA NAME"].value_counts()

# Identify peak night crime location (area with most night crimes)
peak_night_crime_location = night_crime_by_area.index[0]
peak_night_crimes_count = night_crime_by_area.iloc[0]

# Display comprehensive results
print("🌙 NIGHT CRIME HOTSPOT ANALYSIS RESULTS")
print("=" * 45)
print(f"Night Crime Definition: 22:00-03:59 (10 PM - 3:59 AM)")
print(f"Total Night Crimes: {len(night_crimes):,} ({len(night_crimes)/len(crimes)*100:.1f}% of all crimes)")
print(f"Peak Night Crime Location: {peak_night_crime_location}")
print(f"Night Crimes in Peak Area: {peak_night_crimes_count:,}")
print(f"Percentage of Total Night Crimes: {(peak_night_crimes_count/len(night_crimes)*100):.1f}%")

"""
NIGHT CRIME GEOGRAPHICAL VISUALIZATION
======================================

This section creates visualizations to show the geographical distribution
of night crimes across different areas using seaborn for clean presentation.

Visualization Features:
- Horizontal bar chart using seaborn
- Clean, readable code structure
- Professional styling and insights
"""

# Prepare data for visualization (top 15 areas)
top_night_areas = night_crime_by_area.head(15)
night_viz_data = pd.DataFrame({
    'Area': top_night_areas.index,
    'Night_Crimes': top_night_areas.values,
    'Is_Peak': top_night_areas.index == peak_night_crime_location
})

# Create horizontal bar plot using seaborn
fig, ax = plt.subplots(figsize=(14, 10))

sns.barplot(
    data=night_viz_data,
    y='Area',
    x='Night_Crimes',
    hue='Is_Peak',
    palette={False: 'steelblue', True: 'crimson'},
    legend=False,
    ax=ax
)

# Customize the plot
ax.set_title(
    '🌙 Night Crime Distribution by Area (Top 15)\nLos Angeles Crime Data Analysis',
    fontsize=16, fontweight='bold', pad=20
)
ax.set_xlabel('Number of Night Crimes (22:00 - 03:59)', fontsize=12, fontweight='bold')
ax.set_ylabel('Police Area', fontsize=12, fontweight='bold')

# Add value labels on bars
for i, (area, count, is_peak) in enumerate(zip(night_viz_data['Area'], 
                                               night_viz_data['Night_Crimes'],
                                               night_viz_data['Is_Peak'])):
    label_weight = 'bold' if is_peak else 'normal'
    ax.text(count + 10, i, f'{count:,}', va='center', fontweight=label_weight)

plt.tight_layout()
plt.show()

# Display comprehensive statistical summary
print("\n🌙 NIGHT CRIME ANALYSIS SUMMARY")
print("=" * 40)
print(f"Night Crime Time Window: 22:00-03:59 (10 PM - 3:59 AM)")
print(f"Total Night Crimes: {len(night_crimes):,} ({len(night_crimes)/len(crimes)*100:.1f}% of all crimes)")
print(f"Peak Night Crime Area: {peak_night_crime_location}")
print(f"Night Crimes in Peak Area: {peak_night_crimes_count:,}")
print(f"Peak Area Share: {(peak_night_crimes_count/len(night_crimes)*100):.1f}% of night crimes")

# Display top night crime areas
print("\n🏆 TOP 5 NIGHT CRIME AREAS:")
print("-" * 35)
for i, (area, count) in enumerate(night_crime_by_area.head(5).items(), 1):
    percentage = (count / len(night_crimes)) * 100
    print(f"{i}. {area:<20} - {count:,} crimes ({percentage:.1f}%)")

# Generate business insights
print("\n💡 BUSINESS INSIGHTS:")
print("-" * 20)
print(f"• {peak_night_crime_location} requires enhanced night patrol presence")
print(f"• Night crimes represent {len(night_crimes)/len(crimes)*100:.1f}% of total crime volume")
print(f"• Top 5 areas account for {night_crime_by_area.head(5).sum()/len(night_crimes)*100:.1f}% of night crimes")
print("• Consider implementing targeted night safety programs in high-crime areas")
print("• Coordinate with local businesses for improved lighting and security measures")

"""
VICTIM AGE DEMOGRAPHICS ANALYSIS
================================

This analysis examines crime distribution across different victim age groups
using pd.cut for clean, efficient age categorization and readable code.

Data Processing Steps:
1. Clean victim age data (remove invalid/missing values)
2. Use pd.cut to create standardized age group categories
3. Calculate crime frequency by age group
4. Generate insights for policy recommendations
"""

# Data quality assessment
print("🔍 DATA QUALITY ASSESSMENT")
print("=" * 30)
print(f"Total records: {len(crimes):,}")
print(f"Records with age data: {crimes['Vict Age'].notna().sum():,}")
print(f"Missing age data: {crimes['Vict Age'].isna().sum():,}")
print(f"Age range: {crimes['Vict Age'].min():.0f} - {crimes['Vict Age'].max():.0f} years")

# Filter for valid ages (0-120 years, excluding missing values)
age_mask = (crimes['Vict Age'].notna()) & (crimes['Vict Age'] >= 0) & (crimes['Vict Age'] <= 120)
valid_ages_data = crimes[age_mask].copy()

print(f"Records with valid ages: {len(valid_ages_data):,} ({len(valid_ages_data)/len(crimes)*100:.1f}% of total)")

# Define age group boundaries and labels using pd.cut for cleaner code
age_bins = [0, 18, 26, 35, 45, 55, 65, 120]
age_labels = ['0-17', '18-25', '26-34', '35-44', '45-54', '55-64', '65+']

# Create age groups using pd.cut (much cleaner than complex if statements)
valid_ages_data['Age_Group'] = pd.cut(
    valid_ages_data['Vict Age'], 
    bins=age_bins, 
    labels=age_labels, 
    include_lowest=True,
    right=False  # Left-inclusive intervals: [0, 18), [18, 26), etc.
)

# Create the required victim_ages Series with specified age groups
victim_ages = valid_ages_data['Age_Group'].value_counts().reindex(age_labels, fill_value=0)

# Display results
print("\n👥 VICTIM AGE DEMOGRAPHICS RESULTS")
print("=" * 40)
print(f"Analysis based on {len(valid_ages_data):,} records with valid age data")
print(f"Age groups analyzed: {len(age_labels)}")

print("\n📊 Crime Distribution by Age Group:")
print("-" * 35)
for age_group, count in victim_ages.items():
    percentage = (count / victim_ages.sum()) * 100
    print(f"{age_group:<8} - {count:,} crimes ({percentage:.1f}%)")

print(f"\nTotal crimes analyzed: {victim_ages.sum():,}")

"""
VICTIM AGE DEMOGRAPHICS VISUALIZATION
====================================

This section creates comprehensive visualizations showing crime distribution
across different victim age groups using seaborn for clean, professional presentation.

Visualization Features:
- Bar chart using seaborn with clean styling
- Readable code structure
- Statistical insights and business recommendations
"""

# Prepare data for visualization
age_viz_data = pd.DataFrame({
    'Age_Group': victim_ages.index,
    'Crime_Count': victim_ages.values
})

# Identify most affected age group
max_age_group = victim_ages.idxmax()
age_viz_data['Is_Peak'] = age_viz_data['Age_Group'] == max_age_group

# Create visualization using seaborn
fig, ax = plt.subplots(figsize=(14, 8))

# Create bar plot with seaborn
sns.barplot(
    data=age_viz_data,
    x='Age_Group',
    y='Crime_Count',
    hue='Is_Peak',
    palette={False: 'steelblue', True: 'crimson'},
    legend=False,
    ax=ax
)

# Customize the plot
ax.set_title(
    '👥 Crime Distribution by Victim Age Groups\nLos Angeles Crime Data Analysis',
    fontsize=16, fontweight='bold', pad=20
)
ax.set_xlabel('Victim Age Groups', fontsize=12, fontweight='bold')
ax.set_ylabel('Number of Crimes', fontsize=12, fontweight='bold')

# Add value labels on top of bars with percentages
total_crimes = victim_ages.sum()
for i, (age_group, count) in enumerate(zip(age_viz_data['Age_Group'], age_viz_data['Crime_Count'])):
    percentage = (count / total_crimes) * 100
    label_weight = 'bold' if age_group == max_age_group else 'normal'
    ax.text(i, count + 100, f'{count:,}\n({percentage:.1f}%)', 
           ha='center', va='bottom', fontweight=label_weight, fontsize=10)

plt.tight_layout()
plt.show()

# Calculate and display statistics
max_count = victim_ages[max_age_group]
min_age_group = victim_ages.idxmin()
min_count = victim_ages[min_age_group]

print("\n📈 DETAILED AGE DEMOGRAPHICS ANALYSIS")
print("=" * 45)
print(f"Total Valid Records Analyzed: {total_crimes:,}")
print(f"Most Affected Age Group: {max_age_group} ({max_count:,} crimes)")
print(f"Least Affected Age Group: {min_age_group} ({min_count:,} crimes)")
print(f"Average Crimes per Age Group: {victim_ages.mean():.0f}")
print(f"Standard Deviation: {victim_ages.std():.0f}")

# Demographic category analysis
youth_crimes = victim_ages['0-17']
adult_crimes = victim_ages[['18-25', '26-34', '35-44', '45-54']].sum()
senior_crimes = victim_ages[['55-64', '65+']].sum()

print("\n🎯 DEMOGRAPHIC INSIGHTS:")
print("-" * 25)
print(f"Youth (0-17): {youth_crimes:,} crimes ({youth_crimes/total_crimes*100:.1f}%)")
print(f"Adults (18-54): {adult_crimes:,} crimes ({adult_crimes/total_crimes*100:.1f}%)")
print(f"Seniors (55+): {senior_crimes:,} crimes ({senior_crimes/total_crimes*100:.1f}%)")

# Generate business recommendations based on most affected group
print("\n💡 BUSINESS RECOMMENDATIONS:")
print("-" * 30)
if max_age_group in ['18-25', '26-34']:
    print("• Young adults are most affected - focus on education and awareness programs")
    print("• Implement targeted safety campaigns in areas frequented by young adults")
elif max_age_group in ['35-44', '45-54']:
    print("• Middle-aged adults are most affected - focus on workplace and residential security")
    print("• Enhance community policing in suburban and business areas")
elif max_age_group in ['55-64', '65+']:
    print("• Senior citizens are most affected - implement elder protection programs")
    print("• Increase patrols in areas with high senior populations")
else:
    print("• Minors are most affected - strengthen school safety and youth protection programs")
    print("• Coordinate with educational institutions for enhanced security measures")

print("• Develop age-specific crime prevention strategies")
print("• Allocate resources proportionally to age group vulnerability")
print(f"• Create community outreach programs targeting the {max_age_group} age group")